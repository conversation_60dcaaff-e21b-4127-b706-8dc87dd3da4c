﻿
using ET.Controls;

namespace HyExcelVsto.Module.WX
{
    partial class frmGPS生成图层
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.复制路径到剪贴板ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.打开所在目录ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.生成新名字ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.radioButtonPolygon = new System.Windows.Forms.RadioButton();
            this.radioButtonPoint = new System.Windows.Forms.RadioButton();
            this.file_Point = new ET.Controls.ETUcFileSelect();
            this.label3 = new System.Windows.Forms.Label();
            this.button_Point_复制路径 = new System.Windows.Forms.Button();
            this.button_Point_生成KML = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.ucERS_Point_来源注释 = new ETRangeSelectControl();
            this.label26 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.ucERS_Point_来源经纬度 = new ETRangeSelectControl();
            this.ucERS_Point_来源名称 = new ETRangeSelectControl();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();

            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.复制路径到剪贴板ToolStripMenuItem,
            this.打开所在目录ToolStripMenuItem,
            this.toolStripMenuItem1,
            this.生成新名字ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(173, 76);
            // 
            // 复制路径到剪贴板ToolStripMenuItem
            // 
            this.复制路径到剪贴板ToolStripMenuItem.Name = "复制路径到剪贴板ToolStripMenuItem";
            this.复制路径到剪贴板ToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.复制路径到剪贴板ToolStripMenuItem.Text = "复制路径到剪贴板";
            this.复制路径到剪贴板ToolStripMenuItem.Click += new System.EventHandler(this.复制路径到剪贴板ToolStripMenuItem_Click);
            // 
            // 打开所在目录ToolStripMenuItem
            // 
            this.打开所在目录ToolStripMenuItem.Name = "打开所在目录ToolStripMenuItem";
            this.打开所在目录ToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.打开所在目录ToolStripMenuItem.Text = "打开所在目录";
            this.打开所在目录ToolStripMenuItem.Click += new System.EventHandler(this.打开所在目录ToolStripMenuItem_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(169, 6);
            // 
            // 生成新名字ToolStripMenuItem
            // 
            this.生成新名字ToolStripMenuItem.Name = "生成新名字ToolStripMenuItem";
            this.生成新名字ToolStripMenuItem.Size = new System.Drawing.Size(172, 22);
            this.生成新名字ToolStripMenuItem.Text = "生成新名字";
            this.生成新名字ToolStripMenuItem.Click += new System.EventHandler(this.生成新名字ToolStripMenuItem_Click);
            // 
            // radioButtonPolygon
            //
            this.radioButtonPolygon.AutoSize = true;
            this.radioButtonPolygon.Location = new System.Drawing.Point(500, 25);
            this.radioButtonPolygon.Name = "radioButtonPolygon";
            this.radioButtonPolygon.Size = new System.Drawing.Size(59, 16);
            this.radioButtonPolygon.TabIndex = 25;
            this.radioButtonPolygon.TabStop = true;
            this.radioButtonPolygon.Text = "多边形";
            this.radioButtonPolygon.UseVisualStyleBackColor = true;
            //
            // radioButtonPoint
            //
            this.radioButtonPoint.AutoSize = true;
            this.radioButtonPoint.Checked = true;
            this.radioButtonPoint.Location = new System.Drawing.Point(459, 25);
            this.radioButtonPoint.Name = "radioButtonPoint";
            this.radioButtonPoint.Size = new System.Drawing.Size(35, 16);
            this.radioButtonPoint.TabIndex = 25;
            this.radioButtonPoint.TabStop = true;
            this.radioButtonPoint.Text = "点";
            this.radioButtonPoint.UseVisualStyleBackColor = true;
            // 
            // file_Point
            //
            this.file_Point.AutoFillLatestValue = true;
            this.file_Point.ContextMenuStrip = this.contextMenuStrip1;
            this.file_Point.DefaultFileExtension = "";
            this.file_Point.FileFilter = "All files (*.*)|*.*";
            this.file_Point.Location = new System.Drawing.Point(83, 105);
            this.file_Point.Name = "file_Point";
            this.file_Point.Size = new System.Drawing.Size(478, 21);
            this.file_Point.TabIndex = 24;
            this.file_Point.UseFolderBrowser = false;
            this.file_Point.UseOpenFileDialog = true;
            // 
            // label3
            //
            this.label3.AutoSize = true;
            this.label3.ContextMenuStrip = this.contextMenuStrip1;
            this.label3.Location = new System.Drawing.Point(24, 145);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(221, 12);
            this.label3.TabIndex = 23;
            this.label3.Text = "Note：只生成行可见且有经纬度坐标的点";
            //
            // button_Point_复制路径
            //
            this.button_Point_复制路径.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.button_Point_复制路径.Location = new System.Drawing.Point(458, 134);
            this.button_Point_复制路径.Name = "button_Point_复制路径";
            this.button_Point_复制路径.Size = new System.Drawing.Size(103, 23);
            this.button_Point_复制路径.TabIndex = 22;
            this.button_Point_复制路径.Text = "复制文件路径";
            this.button_Point_复制路径.UseVisualStyleBackColor = true;
            this.button_Point_复制路径.Click += new System.EventHandler(this.复制路径到剪贴板ToolStripMenuItem_Click);
            //
            // button_Point_生成KML
            //
            this.button_Point_生成KML.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.button_Point_生成KML.Location = new System.Drawing.Point(349, 134);
            this.button_Point_生成KML.Name = "button_Point_生成KML";
            this.button_Point_生成KML.Size = new System.Drawing.Size(103, 23);
            this.button_Point_生成KML.TabIndex = 22;
            this.button_Point_生成KML.Text = "生成KML";
            this.button_Point_生成KML.UseVisualStyleBackColor = true;
            this.button_Point_生成KML.Click += new System.EventHandler(this.button生成KML_Click);
            // 
            // label2
            //
            this.label2.AutoSize = true;
            this.label2.ContextMenuStrip = this.contextMenuStrip1;
            this.label2.Location = new System.Drawing.Point(22, 109);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 16;
            this.label2.Text = "存储路径:";
            //
            // label1
            //
            this.label1.AutoSize = true;
            this.label1.ContextMenuStrip = this.contextMenuStrip1;
            this.label1.Location = new System.Drawing.Point(22, 82);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 16;
            this.label1.Text = "附带信息:";
            // 
            // ucERS_Point_来源注释
            //
            this.ucERS_Point_来源注释.EnableEnterThenSelect = false;
            this.ucERS_Point_来源注释.HideParentForm = true;
            this.ucERS_Point_来源注释.InputPromptText = "请选择：";
            this.ucERS_Point_来源注释.Location = new System.Drawing.Point(83, 78);
            this.ucERS_Point_来源注释.Name = "ucERS_Point_来源注释";
            this.ucERS_Point_来源注释.SelectedRange = null;
            this.ucERS_Point_来源注释.Size = new System.Drawing.Size(478, 21);
            this.ucERS_Point_来源注释.TabIndex = 17;
            //
            // label26
            //
            this.label26.AutoSize = true;
            this.label26.ContextMenuStrip = this.contextMenuStrip1;
            this.label26.Location = new System.Drawing.Point(22, 24);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(47, 12);
            this.label26.TabIndex = 12;
            this.label26.Text = "经纬度:";
            //
            // label29
            //
            this.label29.AutoSize = true;
            this.label29.ContextMenuStrip = this.contextMenuStrip1;
            this.label29.Location = new System.Drawing.Point(22, 56);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(59, 12);
            this.label29.TabIndex = 13;
            this.label29.Text = "显示名称:";
            // 
            // ucERS_Point_来源经纬度
            //
            this.ucERS_Point_来源经纬度.EnableEnterThenSelect = false;
            this.ucERS_Point_来源经纬度.HideParentForm = true;
            this.ucERS_Point_来源经纬度.InputPromptText = "必选项";
            this.ucERS_Point_来源经纬度.Location = new System.Drawing.Point(83, 20);
            this.ucERS_Point_来源经纬度.Name = "ucERS_Point_来源经纬度";
            this.ucERS_Point_来源经纬度.SelectedRange = null;
            this.ucERS_Point_来源经纬度.Size = new System.Drawing.Size(367, 21);
            this.ucERS_Point_来源经纬度.TabIndex = 14;
            //
            // ucERS_Point_来源名称
            //
            this.ucERS_Point_来源名称.EnableEnterThenSelect = false;
            this.ucERS_Point_来源名称.HideParentForm = true;
            this.ucERS_Point_来源名称.InputPromptText = "可留空";
            this.ucERS_Point_来源名称.Location = new System.Drawing.Point(83, 52);
            this.ucERS_Point_来源名称.Name = "ucERS_Point_来源名称";
            this.ucERS_Point_来源名称.SelectedRange = null;
            this.ucERS_Point_来源名称.Size = new System.Drawing.Size(478, 21);
            this.ucERS_Point_来源名称.TabIndex = 15;

            //
            // frmGPS生成图层
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 172);
            this.Controls.Add(this.radioButtonPolygon);
            this.Controls.Add(this.radioButtonPoint);
            this.Controls.Add(this.file_Point);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.button_Point_复制路径);
            this.Controls.Add(this.button_Point_生成KML);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.ucERS_Point_来源注释);
            this.Controls.Add(this.label26);
            this.Controls.Add(this.label29);
            this.Controls.Add(this.ucERS_Point_来源经纬度);
            this.Controls.Add(this.ucERS_Point_来源名称);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frmGPS生成图层";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "GPS生成KML图层";
            this.Load += new System.EventHandler(this.frmGPS生成图层_Load);
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl2;
        private System.Windows.Forms.TabPage tabPageOmap;
        private System.Windows.Forms.Label label1;
        private ETRangeSelectControl ucERS_Point_来源注释;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.Label label29;
        private ETRangeSelectControl ucERS_Point_来源经纬度;
        private ETRangeSelectControl ucERS_Point_来源名称;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button button_Point_生成KML;
        private System.Windows.Forms.Label label3;
        private ET.Controls.ETUcFileSelect file_Point;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 复制路径到剪贴板ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 打开所在目录ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem 生成新名字ToolStripMenuItem;
        private System.Windows.Forms.Button button_Point_复制路径;
        private System.Windows.Forms.RadioButton radioButtonPolygon;
        private System.Windows.Forms.RadioButton radioButtonPoint;

        // KML转换标签页控件声明
        private System.Windows.Forms.TabPage tabPageKmlConverter;
        private System.Windows.Forms.GroupBox groupBoxKmlFiles;
        private System.Windows.Forms.Button btnSelectKmlSource;
        private System.Windows.Forms.Button btnSelectKmlTarget;
        private System.Windows.Forms.TextBox txtKmlSourceFile;
        private System.Windows.Forms.TextBox txtKmlTargetFile;
        private System.Windows.Forms.Label lblKmlSourceFile;
        private System.Windows.Forms.Label lblKmlTargetFile;
        private System.Windows.Forms.Button btnKmlConvert;
        private ET.Controls.ETLogDisplayControl etLogDisplayControlKml;
    }
}