using ET.Controls;

namespace HyExcelVsto.Module.WX
{
    partial class frmKmlConverter
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }
                ReleaseCustomResources();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxKmlFiles = new System.Windows.Forms.GroupBox();
            this.btnSelectKmlTarget = new System.Windows.Forms.Button();
            this.btnSelectKmlSource = new System.Windows.Forms.Button();
            this.txtKmlTargetFile = new System.Windows.Forms.TextBox();
            this.txtKmlSourceFile = new System.Windows.Forms.TextBox();
            this.lblKmlTargetFile = new System.Windows.Forms.Label();
            this.lblKmlSourceFile = new System.Windows.Forms.Label();
            this.btnKmlConvert = new System.Windows.Forms.Button();
            this.etLogDisplayControlKml = new ET.Controls.ETLogDisplayControl();
            this.groupBoxKmlFiles.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxKmlFiles
            // 
            this.groupBoxKmlFiles.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBoxKmlFiles.Controls.Add(this.btnSelectKmlTarget);
            this.groupBoxKmlFiles.Controls.Add(this.btnSelectKmlSource);
            this.groupBoxKmlFiles.Controls.Add(this.txtKmlTargetFile);
            this.groupBoxKmlFiles.Controls.Add(this.txtKmlSourceFile);
            this.groupBoxKmlFiles.Controls.Add(this.lblKmlTargetFile);
            this.groupBoxKmlFiles.Controls.Add(this.lblKmlSourceFile);
            this.groupBoxKmlFiles.Location = new System.Drawing.Point(12, 12);
            this.groupBoxKmlFiles.Name = "groupBoxKmlFiles";
            this.groupBoxKmlFiles.Size = new System.Drawing.Size(560, 80);
            this.groupBoxKmlFiles.TabIndex = 0;
            this.groupBoxKmlFiles.TabStop = false;
            this.groupBoxKmlFiles.Text = "文件选择";
            // 
            // btnSelectKmlTarget
            // 
            this.btnSelectKmlTarget.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSelectKmlTarget.Location = new System.Drawing.Point(479, 50);
            this.btnSelectKmlTarget.Name = "btnSelectKmlTarget";
            this.btnSelectKmlTarget.Size = new System.Drawing.Size(75, 23);
            this.btnSelectKmlTarget.TabIndex = 5;
            this.btnSelectKmlTarget.Text = "浏览...";
            this.btnSelectKmlTarget.UseVisualStyleBackColor = true;
            this.btnSelectKmlTarget.Click += new System.EventHandler(this.BtnSelectKmlTarget_Click);
            // 
            // btnSelectKmlSource
            // 
            this.btnSelectKmlSource.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSelectKmlSource.Location = new System.Drawing.Point(479, 20);
            this.btnSelectKmlSource.Name = "btnSelectKmlSource";
            this.btnSelectKmlSource.Size = new System.Drawing.Size(75, 23);
            this.btnSelectKmlSource.TabIndex = 4;
            this.btnSelectKmlSource.Text = "浏览...";
            this.btnSelectKmlSource.UseVisualStyleBackColor = true;
            this.btnSelectKmlSource.Click += new System.EventHandler(this.BtnSelectKmlSource_Click);
            // 
            // txtKmlTargetFile
            // 
            this.txtKmlTargetFile.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtKmlTargetFile.Location = new System.Drawing.Point(70, 52);
            this.txtKmlTargetFile.Name = "txtKmlTargetFile";
            this.txtKmlTargetFile.Size = new System.Drawing.Size(403, 21);
            this.txtKmlTargetFile.TabIndex = 3;
            // 
            // txtKmlSourceFile
            // 
            this.txtKmlSourceFile.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtKmlSourceFile.Location = new System.Drawing.Point(70, 22);
            this.txtKmlSourceFile.Name = "txtKmlSourceFile";
            this.txtKmlSourceFile.Size = new System.Drawing.Size(403, 21);
            this.txtKmlSourceFile.TabIndex = 2;
            // 
            // lblKmlTargetFile
            // 
            this.lblKmlTargetFile.AutoSize = true;
            this.lblKmlTargetFile.Location = new System.Drawing.Point(15, 55);
            this.lblKmlTargetFile.Name = "lblKmlTargetFile";
            this.lblKmlTargetFile.Size = new System.Drawing.Size(59, 12);
            this.lblKmlTargetFile.TabIndex = 1;
            this.lblKmlTargetFile.Text = "目标文件:";
            // 
            // lblKmlSourceFile
            // 
            this.lblKmlSourceFile.AutoSize = true;
            this.lblKmlSourceFile.Location = new System.Drawing.Point(15, 25);
            this.lblKmlSourceFile.Name = "lblKmlSourceFile";
            this.lblKmlSourceFile.Size = new System.Drawing.Size(47, 12);
            this.lblKmlSourceFile.TabIndex = 0;
            this.lblKmlSourceFile.Text = "源文件:";
            // 
            // btnKmlConvert
            // 
            this.btnKmlConvert.Location = new System.Drawing.Point(12, 105);
            this.btnKmlConvert.Name = "btnKmlConvert";
            this.btnKmlConvert.Size = new System.Drawing.Size(100, 30);
            this.btnKmlConvert.TabIndex = 1;
            this.btnKmlConvert.Text = "开始转换";
            this.btnKmlConvert.UseVisualStyleBackColor = true;
            this.btnKmlConvert.Click += new System.EventHandler(this.BtnKmlConvert_Click);
            // 
            // etLogDisplayControlKml
            // 
            this.etLogDisplayControlKml.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etLogDisplayControlKml.AutoScrollToBottom = true;
            this.etLogDisplayControlKml.CurrentLogLevel = ET.Controls.ETLogDisplayControl.LogLevel.Info;
            this.etLogDisplayControlKml.CustomInitialMessage = null;
            this.etLogDisplayControlKml.Location = new System.Drawing.Point(125, 105);
            this.etLogDisplayControlKml.LogBackColor = System.Drawing.Color.White;
            this.etLogDisplayControlKml.LogFont = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.etLogDisplayControlKml.LogForeColor = System.Drawing.Color.Black;
            this.etLogDisplayControlKml.Margin = new System.Windows.Forms.Padding(2);
            this.etLogDisplayControlKml.MaxLogLines = 1000;
            this.etLogDisplayControlKml.Name = "etLogDisplayControlKml";
            this.etLogDisplayControlKml.ShowInitialMessage = true;
            this.etLogDisplayControlKml.ShowLogLevel = true;
            this.etLogDisplayControlKml.ShowTimestamp = true;
            this.etLogDisplayControlKml.Size = new System.Drawing.Size(447, 183);
            this.etLogDisplayControlKml.TabIndex = 2;
            this.etLogDisplayControlKml.WordWrap = true;
            // 
            // frmKmlConverter
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 300);
            this.Controls.Add(this.groupBoxKmlFiles);
            this.Controls.Add(this.btnKmlConvert);
            this.Controls.Add(this.etLogDisplayControlKml);
            this.MinimumSize = new System.Drawing.Size(600, 339);
            this.Name = "frmKmlConverter";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "KML转换器";
            this.groupBoxKmlFiles.ResumeLayout(false);
            this.groupBoxKmlFiles.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxKmlFiles;
        private System.Windows.Forms.Button btnSelectKmlSource;
        private System.Windows.Forms.Button btnSelectKmlTarget;
        private System.Windows.Forms.TextBox txtKmlSourceFile;
        private System.Windows.Forms.TextBox txtKmlTargetFile;
        private System.Windows.Forms.Label lblKmlSourceFile;
        private System.Windows.Forms.Label lblKmlTargetFile;
        private System.Windows.Forms.Button btnKmlConvert;
        private ET.Controls.ETLogDisplayControl etLogDisplayControlKml;
    }
}
