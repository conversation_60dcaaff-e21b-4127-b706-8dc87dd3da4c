using ET;
using ET.Controls;
using ExtensionsTools;
using HyExcelVsto.Module.WX.KmlConverter;
using System;
using System.IO;
using System.Windows.Forms;

namespace HyExcelVsto.Module.WX
{
    /// <summary>
    /// KML转换器独立窗体
    /// 用于为KML文件中的地标添加备注信息，包含地标名称、经纬度和区域信息
    /// </summary>
    public partial class frmKmlConverter : Form
    {
        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public frmKmlConverter()
        {
            InitializeComponent();
            InitializeKmlConverter();

            ETLogManager.Info(this, "KML转换器窗体初始化完成");
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化KML转换器
        /// 不设置默认路径，由用户手动选择文件
        /// </summary>
        private void InitializeKmlConverter()
        {
            try
            {
                // 不设置默认路径，保持文本框为空
                txtKmlSourceFile.Text = string.Empty;
                txtKmlTargetFile.Text = string.Empty;

                // 初始化日志控件
                etLogDisplayControlKml.WordWrap = true; // 启用自动换行，解决日志不换行问题
                etLogDisplayControlKml.WriteInfo("KML转换器已就绪");
                etLogDisplayControlKml.WriteInfo("功能说明：为KML文件中的地标添加备注信息");
                etLogDisplayControlKml.WriteInfo("备注格式：地标名称 + 经纬度 + 区域信息");
                etLogDisplayControlKml.WriteInfo("请先选择来源KML文件，目标文件路径将自动生成");

                ETLogManager.Info(this, "KML转换器初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "初始化KML转换器失败", ex);
            }
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// 选择KML源文件按钮点击事件
        /// </summary>
        private void BtnSelectKmlSource_Click(object sender, EventArgs e)
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Title = "选择KML源文件";
                    openFileDialog.Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*";
                    openFileDialog.FilterIndex = 1;

                    if (!string.IsNullOrEmpty(txtKmlSourceFile.Text))
                    {
                        openFileDialog.InitialDirectory = Path.GetDirectoryName(txtKmlSourceFile.Text);
                        openFileDialog.FileName = Path.GetFileName(txtKmlSourceFile.Text);
                    }

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        txtKmlSourceFile.Text = openFileDialog.FileName;
                        etLogDisplayControlKml.WriteInfo($"已选择源文件：{openFileDialog.FileName}");

                        // 自动生成目标文件路径
                        AutoGenerateTargetFilePath(openFileDialog.FileName);
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "选择KML源文件失败", ex);
                MessageBox.Show($"选择文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 选择KML目标文件按钮点击事件
        /// </summary>
        private void BtnSelectKmlTarget_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Title = "选择KML目标文件";
                    saveFileDialog.Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*";
                    saveFileDialog.FilterIndex = 1;

                    if (!string.IsNullOrEmpty(txtKmlTargetFile.Text))
                    {
                        saveFileDialog.InitialDirectory = Path.GetDirectoryName(txtKmlTargetFile.Text);
                        saveFileDialog.FileName = Path.GetFileName(txtKmlTargetFile.Text);
                    }

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        txtKmlTargetFile.Text = saveFileDialog.FileName;
                        etLogDisplayControlKml.WriteInfo($"已选择目标文件：{saveFileDialog.FileName}");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "选择KML目标文件失败", ex);
                MessageBox.Show($"选择文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// KML转换按钮点击事件
        /// </summary>
        private void BtnKmlConvert_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtKmlSourceFile.Text))
                {
                    MessageBox.Show("请选择源KML文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    btnSelectKmlSource.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtKmlTargetFile.Text))
                {
                    MessageBox.Show("请选择目标KML文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    btnSelectKmlTarget.Focus();
                    return;
                }

                if (!File.Exists(txtKmlSourceFile.Text))
                {
                    MessageBox.Show("源文件不存在，请重新选择", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    btnSelectKmlSource.Focus();
                    return;
                }

                // 禁用按钮，显示进度
                btnKmlConvert.Enabled = false;
                btnKmlConvert.Text = "转换中...";
                etLogDisplayControlKml.WriteInfo($"[{DateTime.Now:HH:mm:ss}] 开始转换...");
                System.Windows.Forms.Application.DoEvents();

                // 执行转换
                bool success = KmlConverterHelper.ConvertKml(txtKmlSourceFile.Text, txtKmlTargetFile.Text);

                if (success)
                {
                    etLogDisplayControlKml.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ✅ 转换成功！");
                    etLogDisplayControlKml.WriteInfo($"[{DateTime.Now:HH:mm:ss}] 目标文件：{txtKmlTargetFile.Text}");

                    MessageBox.Show($"KML文件转换成功！\n\n目标文件：{txtKmlTargetFile.Text}",
                                  "转换成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    etLogDisplayControlKml.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ❌ 转换失败");
                    MessageBox.Show("转换失败，请查看日志了解详细信息", "转换失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                etLogDisplayControlKml.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ❌ 转换异常：{ex.Message}");
                MessageBox.Show($"转换时发生异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error(this, $"转换KML文件异常: {ex.Message}", ex);
            }
            finally
            {
                // 恢复按钮状态
                btnKmlConvert.Enabled = true;
                btnKmlConvert.Text = "开始转换";
            }
        }

        #endregion 事件处理

        #region 辅助方法

        /// <summary>
        /// 自动生成目标文件路径
        /// 当目标文件路径为空白，且设定了来源文件路径后，自动生成目标文件路径
        /// 目标文件与来源文件同文件夹，目标文件名增加后缀"_转换"
        /// </summary>
        /// <param name="sourceFilePath">来源文件路径</param>
        private void AutoGenerateTargetFilePath(string sourceFilePath)
        {
            try
            {
                // 只有当目标文件路径为空白，且来源文件路径不为空时才自动生成
                if (string.IsNullOrWhiteSpace(txtKmlTargetFile.Text) && !string.IsNullOrWhiteSpace(sourceFilePath))
                {
                    string sourceDir = Path.GetDirectoryName(sourceFilePath);
                    string sourceFileName = Path.GetFileNameWithoutExtension(sourceFilePath);
                    string sourceExtension = Path.GetExtension(sourceFilePath);

                    // 生成目标文件路径：同文件夹 + 文件名_转换 + 原扩展名
                    string targetFilePath = Path.Combine(sourceDir, $"{sourceFileName}_转换{sourceExtension}");
                    txtKmlTargetFile.Text = targetFilePath;

                    etLogDisplayControlKml.WriteInfo($"自动生成目标文件：{targetFilePath}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "自动生成目标文件路径失败", ex);
            }
        }

        #endregion 辅助方法

        #region 资源释放

        /// <summary>
        /// 释放自定义资源
        /// </summary>
        private void ReleaseCustomResources()
        {
            try
            {
                // 窗体资源清理（如果有需要的话）
                ETLogManager.Info(this, "KML转换器窗体资源已释放");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "释放资源时发生异常", ex);
            }
        }

        #endregion 资源释放
    }
}
